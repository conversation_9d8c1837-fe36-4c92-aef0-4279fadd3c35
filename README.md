# Emergency Communications Platform - Ghana

A real-time emergency communications platform for Ghana with AI-powered text summarization.

## Features

- **Real-time Weather Data**: Fetches weather information from Open-Meteo API
- **Agricultural Advisories**: Scrapes GHAAP agricultural data
- **CAP Alert Processing**: Handles Common Alerting Protocol (CAP) XML messages
- **AI Text Summarization**: Automatically summarizes advisory text using Ollama or OpenAI
- **Interactive 3D Map**: Displays Ghana regions with Three.js
- **Responsive UI**: Glass-morphism design with wider advisory cards

## AI Text Summarization

The platform now includes AI-powered text summarization to make advisory messages more concise and readable:

### Supported AI Services

1. **Ollama** (Default, Free, Local)
   - Runs locally on your machine
   - No API costs
   - Requires Ollama installation

2. **OpenAI** (Cloud-based, Requires API Key)
   - Uses GPT models
   - Requires OpenAI API key
   - Pay-per-use pricing

### Configuration

1. Copy `.env.example` to `.env`
2. Set your preferred AI service:
   ```
   AI_SERVICE=ollama  # or 'openai'
   ```

#### For Ollama:
```
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3
```

#### For OpenAI:
```
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
```

### How It Works

- Advisory texts are automatically summarized to ~200 characters
- Original text is preserved in `original_advisory_text` field
- Falls back to simple truncation if AI services are unavailable
- Summaries focus on emergency-relevant information

## Installation

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure AI service (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your preferences
   ```

3. Run the application:
   ```bash
   python app.py
   ```

4. Open http://localhost:5000 in your browser

## Usage

- Click data source buttons to fetch real-time information
- Advisory cards now display AI-summarized text for better readability
- Cards are wider (500px) to accommodate longer text
- All original data is preserved for reference

## Dependencies

- Flask & Flask-SocketIO for web framework
- Requests & BeautifulSoup for web scraping
- OpenAI for AI summarization (optional)
- ScrapeGraphAI for enhanced scraping
- Three.js for 3D visualization
