import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import json
from scrapegraphai.graphs import SmartScraperGraph
from scrapegraphai.utils import prettify_exec_info
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def scrape_ghaap_with_ai(url, llm_model="ollama/llama3"):
    """
    Uses ScrapeGraphAI to intelligently scrape agricultural advisory data from GHAAP website.

    Args:
        url (str): The GHAAP URL to scrape
        llm_model (str): The LLM model to use for scraping (default: ollama/llama3)

    Returns:
        dict: Structured data containing agricultural advisory information
    """
    logging.info(f"Starting AI-powered scraping of URL: {url}")

    # Define the prompt for what we want to extract
    prompt = """
    Extract agricultural advisory information from this GHAAP (Ghana Agriculture and Agribusiness Portal) webpage.
    Please provide the following information in a structured format:

    1. Weather forecast data (temperature, rainfall, humidity if available)
    2. Agricultural recommendations or advisories
    3. Crop-specific information (planting recommendations, harvest timing, etc.)
    4. Regional information (which region/district this applies to)
    5. Date/time information for the forecast or advisory
    6. Any warnings or alerts related to farming conditions
    7. Seasonal recommendations

    Return the data as a JSON object with clear field names.
    If certain information is not available, mark it as "Not available" or null.
    """

    # Configuration for ScrapeGraphAI
    graph_config = {
        "llm": {
            "model": llm_model,
            "temperature": 0.1,  # Low temperature for more consistent results
        },
        "verbose": True,
        "headless": True,  # Run browser in headless mode
    }

    try:
        # Create the SmartScraperGraph
        smart_scraper_graph = SmartScraperGraph(
            prompt=prompt,
            source=url,
            config=graph_config
        )

        # Run the scraper
        logging.info("Running AI scraper...")
        result = smart_scraper_graph.run()

        logging.info("AI scraping completed successfully")
        return result

    except Exception as e:
        logging.error(f"Error during AI scraping: {e}")
        return {"error": str(e), "advisory_text": "Failed to scrape data using AI method"}

def scrape_ghaap_weather_forecast(url):
    """
    Enhanced scraping function for GHAAP website with better error handling and multiple fallback strategies.
    """
    print(f"Attempting to scrape URL: {url}")

    # Define multiple User-Agent headers to try
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
    ]

    for i, user_agent in enumerate(user_agents):
        headers = {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        try:
            print(f"Attempt {i+1}: Using User-Agent: {user_agent[:50]}...")

            # Fetch the content of the page with timeout
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            # Parse the HTML content
            soup = BeautifulSoup(response.text, 'html.parser')

            # Try multiple table selectors
            table_selectors = [
                'table.table.table-striped.table-bordered.table-hover',
                'table.table-striped',
                'table.table-bordered',
                'table',
                '.table',
                '#forecast-table',
                '[class*="table"]'
            ]

            table = None
            for selector in table_selectors:
                table = soup.select_one(selector)
                if table:
                    print(f"Found table using selector: {selector}")
                    print(f"Table HTML preview: {str(table)[:200]}...")
                    break

            if not table:
                # Table is empty or doesn't exist - try to find other content
                print(f"Attempt {i+1}: Table empty or not found, looking for alternative content...")

                # Look for any content that might contain agricultural data
                content_selectors = [
                    'div[class*="content"]',
                    'div[class*="forecast"]',
                    'div[class*="advisory"]',
                    'div[class*="weather"]',
                    'div[class*="crop"]',
                    'section',
                    'article',
                    '.panel-body',
                    '.card-body',
                    'main'
                ]

                text_content = []
                for selector in content_selectors:
                    elements = soup.select(selector)
                    for element in elements[:2]:  # Limit to first 2 per selector
                        text = element.get_text(strip=True)
                        if len(text) > 100:  # Only include substantial content
                            text_content.append(text)

                # Also try to get any text from the page that might be relevant
                page_text = soup.get_text()
                if len(page_text) > 500:
                    # Look for agricultural keywords in the page
                    agro_keywords = ['forecast', 'weather', 'crop', 'advisory', 'temperature', 'rainfall', 'planting', 'harvest', 'season']
                    if any(keyword.lower() in page_text.lower() for keyword in agro_keywords):
                        # Extract relevant paragraphs
                        paragraphs = soup.find_all('p')
                        for p in paragraphs[:5]:
                            p_text = p.get_text(strip=True)
                            if len(p_text) > 50 and any(keyword.lower() in p_text.lower() for keyword in agro_keywords):
                                text_content.append(p_text)

                if text_content:
                    print(f"Found {len(text_content)} content areas with potential agricultural data")
                    return create_advisory_from_text(text_content, url)

                print(f"Attempt {i+1}: No relevant content found")
                continue

            # Extract table data
            headers_list = []
            tbody = table.find('tbody')
            thead = table.find('thead')

            if thead:
                for th in thead.find_all(['th', 'td']):
                    headers_list.append(th.get_text(strip=True))

            # Extract table rows
            data_rows = []
            rows = tbody.find_all('tr') if tbody else table.find_all('tr')

            print(f"Found {len(rows)} rows in table")

            for j, row in enumerate(rows):
                columns = [td.get_text(strip=True) for td in row.find_all(['td', 'th'])]
                print(f"Row {j+1}: {len(columns)} columns - {columns[:3] if columns else 'No columns'}")
                if columns and len(columns) > 0:  # Accept any row with data
                    data_rows.append(columns)

            print(f"Extracted {len(data_rows)} data rows")

            if data_rows:
                # Ensure headers match data columns
                if not headers_list or len(headers_list) != len(data_rows[0]):
                    headers_list = [f"Column_{i+1}" for i in range(len(data_rows[0]))]

                df = pd.DataFrame(data_rows, columns=headers_list)
                print(f"Scraping successful! Found {len(df)} rows of data.")
                return df
            else:
                print(f"Attempt {i+1}: Table found but no data rows extracted")
                # Try to extract content from the page instead
                print(f"Attempt {i+1}: Looking for alternative content...")

                # Look for any content that might contain agricultural data
                content_selectors = [
                    'div[class*="content"]',
                    'div[class*="forecast"]',
                    'div[class*="advisory"]',
                    'div[class*="weather"]',
                    'div[class*="crop"]',
                    'section',
                    'article',
                    '.panel-body',
                    '.card-body',
                    'main'
                ]

                text_content = []
                for selector in content_selectors:
                    elements = soup.select(selector)
                    for element in elements[:2]:  # Limit to first 2 per selector
                        text = element.get_text(strip=True)
                        if len(text) > 100:  # Only include substantial content
                            text_content.append(text)

                # Also try to get any text from the page that might be relevant
                page_text = soup.get_text()
                if len(page_text) > 500:
                    # Look for agricultural keywords in the page
                    agro_keywords = ['forecast', 'weather', 'crop', 'advisory', 'temperature', 'rainfall', 'planting', 'harvest', 'season']
                    if any(keyword.lower() in page_text.lower() for keyword in agro_keywords):
                        # Extract relevant paragraphs
                        paragraphs = soup.find_all('p')
                        for p in paragraphs[:5]:
                            p_text = p.get_text(strip=True)
                            if len(p_text) > 50 and any(keyword.lower() in p_text.lower() for keyword in agro_keywords):
                                text_content.append(p_text)

                if text_content:
                    print(f"Found {len(text_content)} content areas with potential agricultural data")
                    return create_advisory_from_text(text_content, url)

                continue

        except requests.exceptions.RequestException as e:
            print(f"Attempt {i+1}: Network error: {e}")
            continue
        except Exception as e:
            print(f"Attempt {i+1}: Unexpected error: {e}")
            continue

    print("All scraping attempts failed.")
    return pd.DataFrame()

def create_advisory_from_text(text_content, url):
    """
    Creates an advisory DataFrame from extracted text content when table scraping fails.
    """
    advisory_data = []

    for i, text in enumerate(text_content):
        # Look for weather-related keywords and extract relevant information
        weather_keywords = ['temperature', 'rainfall', 'humidity', 'wind', 'forecast', 'advisory', 'crop', 'planting']

        relevant_sentences = []
        sentences = text.split('.')

        for sentence in sentences:
            if any(keyword.lower() in sentence.lower() for keyword in weather_keywords):
                relevant_sentences.append(sentence.strip())

        if relevant_sentences:
            advisory_data.append({
                'Section': f'Content_Area_{i+1}',
                'Advisory_Text': ' | '.join(relevant_sentences[:3]),  # Limit to first 3 relevant sentences
                'Source': 'GHAAP_Text_Extraction',
                'URL': url
            })

    if advisory_data:
        return pd.DataFrame(advisory_data)
    else:
        return pd.DataFrame()

def scrape_ghaap_hybrid(url, use_ai=False, llm_model="ollama/llama3"):
    """
    Hybrid scraping function that tries AI scraping first, then falls back to traditional scraping.

    Args:
        url (str): The GHAAP URL to scrape
        use_ai (bool): Whether to try AI scraping first
        llm_model (str): The LLM model to use for AI scraping

    Returns:
        dict: Structured data containing agricultural advisory information
    """
    logging.info(f"Starting hybrid scraping of URL: {url}")

    if use_ai:
        try:
            logging.info("Attempting AI-powered scraping...")
            ai_result = scrape_ghaap_with_ai(url, llm_model)

            if ai_result and "error" not in ai_result:
                logging.info("AI scraping successful!")
                return {
                    "method": "ai",
                    "data": ai_result,
                    "advisory_text": format_ai_result_for_advisory(ai_result),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "source": "GHAAP (AI-scraped)"
                }
            else:
                logging.warning("AI scraping failed, falling back to traditional scraping...")
        except Exception as e:
            logging.warning(f"AI scraping error: {e}, falling back to traditional scraping...")

    # Fallback to traditional scraping
    logging.info("Using traditional scraping method...")
    df_result = scrape_ghaap_weather_forecast(url)

    if not df_result.empty:
        return {
            "method": "traditional",
            "data": df_result.to_dict('records'),
            "advisory_text": format_dataframe_for_advisory(df_result),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "source": "GHAAP (Traditional scraping)"
        }
    else:
        return {
            "method": "failed",
            "error": "Both AI and traditional scraping methods failed",
            "advisory_text": "Unable to retrieve agricultural advisory data from GHAAP website.",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "source": "GHAAP"
        }

def format_ai_result_for_advisory(ai_result):
    """
    Formats AI scraping result into a readable advisory text.
    """
    if isinstance(ai_result, dict):
        advisory_parts = []

        # Extract key information
        if "agricultural_recommendations" in ai_result:
            advisory_parts.append(f"Agricultural Recommendations: {ai_result['agricultural_recommendations']}")

        if "weather_forecast" in ai_result:
            advisory_parts.append(f"Weather Forecast: {ai_result['weather_forecast']}")

        if "crop_information" in ai_result:
            advisory_parts.append(f"Crop Information: {ai_result['crop_information']}")

        if "regional_information" in ai_result:
            advisory_parts.append(f"Region: {ai_result['regional_information']}")

        if "warnings" in ai_result:
            advisory_parts.append(f"Warnings: {ai_result['warnings']}")

        if advisory_parts:
            return " | ".join(advisory_parts)
        else:
            # If structured data is not available, try to extract any text
            return str(ai_result)[:500] + "..." if len(str(ai_result)) > 500 else str(ai_result)

    return str(ai_result)[:500] + "..." if len(str(ai_result)) > 500 else str(ai_result)

def get_ghaap_advisory(url=None, use_ai=False):
    """
    Main function to get GHAAP agricultural advisory data.
    This function can be easily imported and used by other modules like Flask app.

    Args:
        url (str, optional): Custom URL to scrape. If None, uses default GHAAP URL.
        use_ai (bool): Whether to use AI scraping first

    Returns:
        dict: Structured advisory data
    """
    if url is None:
        # Default GHAAP URL for Greater Accra Region, Ga East District, Maize crop
        url = "https://ghaap.com/weather-forecast/index.php?p=crop-search-event®iontxt=REG02&districttxt=DS028&crop=CT0000000008&yeartxt=2025"

    return scrape_ghaap_hybrid(url, use_ai=use_ai)

def format_dataframe_for_advisory(df):
    """
    Formats DataFrame result into a readable advisory text.
    """
    if df.empty:
        return "No agricultural data available."

    # Try to create a summary from the DataFrame
    summary_parts = []

    # Get first few rows as summary
    for _, row in df.head(3).iterrows():
        row_text = " | ".join([f"{col}: {val}" for col, val in row.items() if pd.notna(val)])
        if row_text:
            summary_parts.append(row_text)

    if summary_parts:
        return " || ".join(summary_parts)
    else:
        return f"Agricultural data available with {len(df)} records. Check detailed output for more information."

# --- Main execution ---
if __name__ == "__main__":
    target_url = "https://ghaap.com/weather-forecast/index.php?p=crop-search-event®iontxt=REG02&districttxt=DS028&crop=CT0000000008&yeartxt=2025"
    output_filename = "ghaap_weather_forecast_data.csv"
    json_output_filename = "ghaap_ai_scraped_data.json"

    print("=== GHAAP Agricultural Data Scraper (Enhanced with AI) ===\n")

    # Try hybrid scraping (Traditional first, AI disabled for now)
    print("Attempting enhanced traditional scraping...")
    result = scrape_ghaap_hybrid(target_url, use_ai=False, llm_model="ollama/llama3")

    print(f"\n--- Scraping Results ---")
    print(f"Method used: {result.get('method', 'unknown')}")
    print(f"Source: {result.get('source', 'unknown')}")
    print(f"Timestamp: {result.get('timestamp', 'unknown')}")

    if "error" in result:
        print(f"Error: {result['error']}")

    print(f"\n--- Advisory Text ---")
    print(result.get('advisory_text', 'No advisory text available'))

    # Save results
    if result.get('method') == 'ai':
        # Save AI results as JSON
        try:
            with open(json_output_filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\nAI scraping results saved to {json_output_filename}")
        except Exception as e:
            print(f"Error saving AI results: {e}")

    elif result.get('method') == 'traditional' and result.get('data'):
        # Save traditional results as CSV
        try:
            df = pd.DataFrame(result['data'])
            df.to_csv(output_filename, index=False, encoding='utf-8')
            print(f"\nTraditional scraping results saved to {output_filename}")

            print("\n--- Scraped Data (first 5 rows) ---")
            print(df.head())
        except Exception as e:
            print(f"Error saving traditional results: {e}")

    # Also save a summary JSON file regardless of method
    summary_filename = "ghaap_scraping_summary.json"
    summary = {
        "url": target_url,
        "method": result.get('method'),
        "timestamp": result.get('timestamp'),
        "advisory_text": result.get('advisory_text'),
        "source": result.get('source'),
        "success": "error" not in result
    }

    try:
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        print(f"\nScraping summary saved to {summary_filename}")
    except Exception as e:
        print(f"Error saving summary: {e}")

    print("\n=== Script finished ===")